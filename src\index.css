@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Ubud Colors */
:root {
  --ubud-dark-green: #626f47;
  --ubud-light-green: #a4b465;
  --ubud-yellow: #ffcf50;
  --ubud-cream: #fefae0;
}

@layer utilities {
  /* Ubud Color Utilities */
  .bg-ubud-dark-green {
    background-color: var(--ubud-dark-green);
  }

  .bg-ubud-light-green {
    background-color: var(--ubud-light-green);
  }

  .bg-ubud-yellow {
    background-color: var(--ubud-yellow);
  }

  .bg-ubud-cream {
    background-color: var(--ubud-cream);
  }

  .text-ubud-dark-green {
    color: var(--ubud-dark-green);
  }

  .text-ubud-light-green {
    color: var(--ubud-light-green);
  }

  .text-ubud-yellow {
    color: var(--ubud-yellow);
  }

  .text-ubud-cream {
    color: var(--ubud-cream);
  }

  .border-ubud-dark-green {
    border-color: var(--ubud-dark-green);
  }

  .from-ubud-dark-green {
    --tw-gradient-from: var(--ubud-dark-green);
    --tw-gradient-to: rgb(98 111 71 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-ubud-light-green {
    --tw-gradient-to: var(--ubud-light-green);
  }

  .from-ubud-light-green {
    --tw-gradient-from: var(--ubud-light-green);
    --tw-gradient-to: rgb(164 180 101 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-ubud-dark-green {
    --tw-gradient-to: var(--ubud-dark-green);
  }

  .hover\:bg-ubud-light-green:hover {
    background-color: var(--ubud-light-green);
  }

  .focus\:border-ubud-dark-green:focus {
    border-color: var(--ubud-dark-green);
  }

  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}
