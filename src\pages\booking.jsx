import { useState } from "react";
import { useNavigate } from "react-router-dom";

export default function Booking() {
  const navigate = useNavigate();

  // State untuk modal data diri
  const [showPersonalDataModal, setShowPersonalDataModal] = useState(true);
  const [personalData, setPersonalData] = useState({
    name: "",
    phone: "",
    activityDate: "",
    hotel: "",
    needTransport: null, // true/false
    transportType: "",
    driverPhone: "",
  });

  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    date: "",
    guests: 1,
    mainActivity: "",
  });

  // Dynamic additional activities with individual guest counts
  const [additionalActivities, setAdditionalActivities] = useState([]);

  const [selectedActivities, setSelectedActivities] = useState([null]);

  const services = [
    { id: 1, name: "Trekking and Jeep", price: 150000 },
    { id: 2, name: "Swing and Nest", price: 150000 },
    { id: 3, name: "Rafting", price: 150000 },
    { id: 4, name: "ATV Ride", price: 150000 },
  ];

  // Handler untuk personal data modal
  const handlePersonalDataChange = (e) => {
    const { name, value, type } = e.target;
    setPersonalData({
      ...personalData,
      [name]: type === "radio" ? value === "true" : value,
    });
  };

  const handleTransportChange = (value) => {
    setPersonalData({
      ...personalData,
      needTransport: value,
      transportType: value ? personalData.transportType : "",
      driverPhone: value ? "" : personalData.driverPhone,
    });
  };

  const validatePersonalData = () => {
    const {
      name,
      phone,
      activityDate,
      hotel,
      needTransport,
      transportType,
      driverPhone,
    } = personalData;

    if (!name || !phone || !activityDate || !hotel || needTransport === null) {
      return false;
    }

    if (needTransport && !transportType) {
      return false;
    }

    if (!needTransport && !driverPhone) {
      return false;
    }

    return true;
  };

  const handlePersonalDataSubmit = () => {
    if (!validatePersonalData()) {
      alert("Mohon lengkapi semua data yang diperlukan");
      return;
    }

    // Transfer data ke form booking
    setFormData({
      ...formData,
      name: personalData.name,
      phone: personalData.phone,
      date: personalData.activityDate,
    });

    setShowPersonalDataModal(false);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  // Add new additional activity
  const addAdditionalActivity = () => {
    const newActivity = {
      id: Date.now(), // Simple ID generation
      activityName: "",
      guests: formData.guests, // Default to main activity guest count
      selectedService: null,
    };
    setAdditionalActivities([...additionalActivities, newActivity]);
  };

  // Remove additional activity
  const removeAdditionalActivity = (activityId) => {
    setAdditionalActivities(
      additionalActivities.filter((activity) => activity.id !== activityId)
    );
  };

  // Update additional activity
  const updateAdditionalActivity = (activityId, field, value) => {
    setAdditionalActivities(
      additionalActivities.map((activity) => {
        if (activity.id === activityId) {
          const updatedActivity = { ...activity, [field]: value };

          // If activity name changed, update the selected service
          if (field === "activityName") {
            const service = services.find((s) => s.name === value);
            updatedActivity.selectedService = service || null;
          }

          // If guests field changed, handle it more flexibly
          if (field === "guests") {
            // Allow empty string during typing, but store as number when valid
            if (value === "" || value === "0") {
              updatedActivity.guests = value; // Allow temporary empty/zero state
            } else {
              const numValue = parseInt(value);
              updatedActivity.guests =
                isNaN(numValue) || numValue < 1 ? 1 : numValue;
            }
          }

          return updatedActivity;
        }
        return activity;
      })
    );
  };

  const handleActivityChange = (index, activityName) => {
    const activity = services.find((s) => s.name === activityName);
    const newSelectedActivities = [...selectedActivities];
    newSelectedActivities[index] = activity;
    setSelectedActivities(newSelectedActivities);

    if (index === 0) {
      setFormData({ ...formData, mainActivity: activityName });
    }
  };

  const calculateTotal = () => {
    // Calculate main activity cost
    const mainActivityCost = selectedActivities[0]
      ? selectedActivities[0].price * formData.guests
      : 0;

    // Calculate additional activities cost
    const additionalCost = additionalActivities.reduce((total, activity) => {
      if (activity.selectedService) {
        return total + activity.selectedService.price * activity.guests;
      }
      return total;
    }, 0);

    return mainActivityCost + additionalCost;
  };

  const handleMakeBook = () => {
    if (
      !formData.name ||
      !formData.phone ||
      !formData.date ||
      !formData.mainActivity
    ) {
      alert("Please fill in all required fields");
      return;
    }

    const bookingData = {
      ...formData,
      selectedActivities: selectedActivities.filter((a) => a !== null),
      additionalActivities: additionalActivities.filter(
        (a) => a.selectedService !== null
      ),
      total: calculateTotal(),
      bookingDate: new Date().toISOString(),
    };

    navigate("/payment", { state: { bookingData } });
  };

  return (
    <>
      {/* Modal Data Diri */}
      {showPersonalDataModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
              <h2 className="text-2xl font-bold text-white text-center">
                Data Diri Peserta
              </h2>
              <p className="text-ubud-cream text-center mt-2">
                Mohon lengkapi data diri sebelum melanjutkan booking
              </p>
            </div>

            <div className="p-6 space-y-4">
              {/* Nama */}
              <div>
                <label className="block text-ubud-dark-green font-semibold mb-2">
                  Nama Lengkap *
                </label>
                <input
                  type="text"
                  name="name"
                  value={personalData.name}
                  onChange={handlePersonalDataChange}
                  placeholder="Masukkan nama lengkap"
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                />
              </div>

              {/* Nomor Telepon */}
              <div>
                <label className="block text-ubud-dark-green font-semibold mb-2">
                  Nomor Telepon *
                </label>
                <input
                  type="tel"
                  name="phone"
                  value={personalData.phone}
                  onChange={handlePersonalDataChange}
                  placeholder="Masukkan nomor telepon"
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                />
              </div>

              {/* Tanggal Kegiatan */}
              <div>
                <label className="block text-ubud-dark-green font-semibold mb-2">
                  Tanggal Kegiatan *
                </label>
                <input
                  type="date"
                  name="activityDate"
                  value={personalData.activityDate}
                  onChange={handlePersonalDataChange}
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                />
              </div>

              {/* Hotel */}
              <div>
                <label className="block text-ubud-dark-green font-semibold mb-2">
                  Hotel *
                </label>
                <input
                  type="text"
                  name="hotel"
                  value={personalData.hotel}
                  onChange={handlePersonalDataChange}
                  placeholder="Masukkan nama hotel"
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                />
              </div>

              {/* Transport */}
              <div>
                <label className="block text-ubud-dark-green font-semibold mb-2">
                  Apakah memerlukan transport? *
                </label>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => handleTransportChange(true)}
                    className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                      personalData.needTransport === true
                        ? "bg-ubud-dark-green text-white"
                        : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                    }`}
                  >
                    Ya
                  </button>
                  <button
                    type="button"
                    onClick={() => handleTransportChange(false)}
                    className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                      personalData.needTransport === false
                        ? "bg-ubud-dark-green text-white"
                        : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                    }`}
                  >
                    Tidak
                  </button>
                </div>
              </div>

              {/* Transport Type - hanya muncul jika memerlukan transport */}
              {personalData.needTransport === true && (
                <div>
                  <label className="block text-ubud-dark-green font-semibold mb-2">
                    Jenis Transport *
                  </label>
                  <select
                    name="transportType"
                    value={personalData.transportType}
                    onChange={handlePersonalDataChange}
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                  >
                    <option value="">Pilih jenis transport</option>
                    <option value="car">Mobil</option>
                    <option value="motorcycle">Motor</option>
                    <option value="van">Van</option>
                    <option value="bus">Bus</option>
                  </select>
                </div>
              )}

              {/* Driver Phone - hanya muncul jika tidak memerlukan transport */}
              {personalData.needTransport === false && (
                <div>
                  <label className="block text-ubud-dark-green font-semibold mb-2">
                    Nomor Telepon Driver *
                  </label>
                  <input
                    type="tel"
                    name="driverPhone"
                    value={personalData.driverPhone}
                    onChange={handlePersonalDataChange}
                    placeholder="Masukkan nomor telepon driver"
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                  />
                </div>
              )}

              {/* Tombol Submit */}
              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={handlePersonalDataSubmit}
                  className="bg-ubud-dark-green text-white px-8 py-3 rounded-lg hover:bg-ubud-light-green transition-colors font-medium"
                >
                  OK, Lanjutkan
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {!showPersonalDataModal && (
        <div className="min-h-screen bg-gradient-to-br from-ubud-light-green to-ubud-dark-green">
          <div className="container mx-auto px-6 py-8">
            <div className="max-w-5xl mx-auto">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl overflow-hidden">
                <div className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green p-6">
                  <h1 className="text-3xl font-bold text-white text-center">
                    Book Your Adventure
                  </h1>
                  <p className="text-ubud-cream text-center mt-2">
                    Fill in the details below to book your exciting activities
                  </p>
                </div>

                <div className="p-8">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Left Side - Form */}
                    <div className="space-y-6">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-ubud-dark-green font-semibold mb-2">
                            Full Name *
                          </label>
                          <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            placeholder="Enter your name"
                            className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-ubud-dark-green font-semibold mb-2">
                            Number of People *
                          </label>
                          <div className="flex items-center">
                            <input
                              type="number"
                              name="guests"
                              value={formData.guests}
                              onChange={handleChange}
                              min="1"
                              className="w-20 px-4 py-3 rounded-l-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                            />
                            <span className="bg-ubud-dark-green text-white px-4 py-3 rounded-r-lg font-medium">
                              People
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-ubud-dark-green font-semibold mb-2">
                            Phone Number *
                          </label>
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleChange}
                            placeholder="input your number"
                            className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-ubud-dark-green font-semibold mb-2">
                            Booking Date *
                          </label>
                          <input
                            type="date"
                            name="date"
                            value={formData.date}
                            onChange={handleChange}
                            className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                            required
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-ubud-dark-green font-semibold mb-2">
                          Main Activity *
                        </label>
                        <select
                          name="mainActivity"
                          value={formData.mainActivity}
                          onChange={(e) => {
                            handleChange(e);
                            handleActivityChange(0, e.target.value);
                          }}
                          className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                          required
                        >
                          <option value="">Select Main Activity</option>
                          {services.map((service) => (
                            <option key={service.id} value={service.name}>
                              {service.name} - Rp.{" "}
                              {service.price.toLocaleString("id-ID")}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Additional Activities Section */}
                      <div className="space-y-4">
                        {/* Display additional activities */}
                        {additionalActivities.map((activity, index) => (
                          <div
                            key={activity.id}
                            className="bg-gray-50 p-4 rounded-lg border-2 border-gray-200"
                          >
                            <div className="flex justify-between items-center mb-3">
                              <h4 className="text-ubud-dark-green font-semibold">
                                Activity {index + 2}
                              </h4>
                              <button
                                onClick={() =>
                                  removeAdditionalActivity(activity.id)
                                }
                                className="text-red-500 hover:text-red-700 text-sm font-medium"
                              >
                                Remove
                              </button>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="block text-ubud-dark-green font-medium mb-2">
                                  Select Activity
                                </label>
                                <select
                                  value={activity.activityName}
                                  onChange={(e) =>
                                    updateAdditionalActivity(
                                      activity.id,
                                      "activityName",
                                      e.target.value
                                    )
                                  }
                                  className="w-full px-3 py-2 rounded-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                                >
                                  <option value="">Choose Activity</option>
                                  {services.map((service) => (
                                    <option
                                      key={service.id}
                                      value={service.name}
                                    >
                                      {service.name} - Rp.{" "}
                                      {service.price.toLocaleString("id-ID")}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div>
                                <label className="block text-ubud-dark-green font-medium mb-2">
                                  Number of People
                                </label>
                                <div className="flex items-center">
                                  <input
                                    type="number"
                                    value={activity.guests}
                                    onChange={(e) =>
                                      updateAdditionalActivity(
                                        activity.id,
                                        "guests",
                                        e.target.value
                                      )
                                    }
                                    onBlur={(e) => {
                                      // Ensure minimum value of 1 when user leaves the field
                                      const value = parseInt(e.target.value);
                                      if (isNaN(value) || value < 1) {
                                        updateAdditionalActivity(
                                          activity.id,
                                          "guests",
                                          1
                                        );
                                      }
                                    }}
                                    min="1"
                                    step="1"
                                    className="w-20 px-3 py-2 rounded-l-lg border-2 border-gray-200 focus:border-ubud-dark-green focus:outline-none transition-colors"
                                    placeholder="1"
                                  />
                                  <span className="bg-ubud-dark-green text-white px-3 py-2 rounded-r-lg font-medium text-sm">
                                    People
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}

                        {/* Add Other Activity Button */}
                        <button
                          onClick={addAdditionalActivity}
                          className="w-full py-3 px-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:text-gray-700 hover:border-gray-400 transition-colors font-medium text-sm bg-gray-50 hover:bg-gray-100"
                        >
                          + Add Other Activity
                        </button>
                      </div>
                    </div>

                    {/* Right Side - Summary */}
                    <div className="bg-gradient-to-br from-ubud-dark-green to-ubud-light-green rounded-xl p-6 text-white h-fit">
                      <h3 className="text-xl font-bold mb-4 flex items-center">
                        <span className="mr-2">🛒</span>
                        Booking Summary
                      </h3>

                      <div className="space-y-3 mb-6">
                        {/* Main Activity */}
                        {selectedActivities
                          .filter((activity) => activity !== null)
                          .map((activity, index) => (
                            <div
                              key={index}
                              className="bg-white/10 rounded-lg p-3"
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <span className="text-sm font-medium">
                                    {activity.name}
                                  </span>
                                  <div className="text-xs text-ubud-cream">
                                    {formData.guests} people × Rp.{" "}
                                    {activity.price.toLocaleString("id-ID")}
                                  </div>
                                </div>
                                <span className="font-semibold">
                                  Rp.{" "}
                                  {(
                                    activity.price * formData.guests
                                  ).toLocaleString("id-ID")}
                                </span>
                              </div>
                            </div>
                          ))}

                        {/* Additional Activities */}
                        {additionalActivities
                          .filter(
                            (activity) => activity.selectedService !== null
                          )
                          .map((activity) => (
                            <div
                              key={activity.id}
                              className="bg-white/10 rounded-lg p-3"
                            >
                              <div className="flex justify-between items-center">
                                <div>
                                  <span className="text-sm font-medium">
                                    {activity.selectedService.name}
                                  </span>
                                  <div className="text-xs text-ubud-cream">
                                    {activity.guests} people × Rp.{" "}
                                    {activity.selectedService.price.toLocaleString(
                                      "id-ID"
                                    )}
                                  </div>
                                </div>
                                <span className="font-semibold">
                                  Rp.{" "}
                                  {(
                                    activity.selectedService.price *
                                    activity.guests
                                  ).toLocaleString("id-ID")}
                                </span>
                              </div>
                            </div>
                          ))}
                      </div>

                      <div className="border-t border-white/20 pt-4 mb-6">
                        <div className="flex justify-between items-center text-lg font-bold">
                          <span>Total Amount</span>
                          <span className="text-ubud-yellow">
                            Rp. {calculateTotal().toLocaleString("id-ID")}
                          </span>
                        </div>
                      </div>

                      <button
                        onClick={handleMakeBook}
                        className="w-full bg-ubud-yellow text-ubud-dark-green py-4 rounded-lg font-bold text-lg hover:bg-yellow-400 transition-colors shadow-lg"
                      >
                        Proceed to Payment
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
